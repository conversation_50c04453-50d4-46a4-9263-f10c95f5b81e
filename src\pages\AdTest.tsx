import React from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import AdSense from '@/components/AdSense';
import AdBanner from '@/components/AdBanner';
import AdSquare from '@/components/AdSquare';
import ResponsiveAd from '@/components/ResponsiveAd';
import { useAdSenseStatus } from '@/hooks/useAdSense';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertCircle, Settings } from 'lucide-react';

// صفحة اختبار الإعلانات (للتطوير فقط)
const AdTest: React.FC = () => {
  const { isScriptLoaded, isEnabled, publisherId } = useAdSenseStatus();

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">اختبار</span> الإعلانات
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              صفحة اختبار شاملة لجميع أنواع الإعلانات المدمجة في الموقع
            </p>
          </div>

          {/* حالة AdSense */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-amber-400 flex items-center">
                <Settings className="w-5 h-5 ml-2" />
                حالة Google AdSense
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  {isEnabled ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                  <span className="text-sm">
                    الإعلانات: {isEnabled ? 'مفعلة' : 'معطلة'}
                  </span>
                </div>
                
                <div className="flex items-center space-x-3 space-x-reverse">
                  {isScriptLoaded ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                  <span className="text-sm">
                    السكريبت: {isScriptLoaded ? 'محمل' : 'غير محمل'}
                  </span>
                </div>
                
                <div className="flex items-center space-x-3 space-x-reverse">
                  <AlertCircle className="w-5 h-5 text-blue-400" />
                  <span className="text-sm">
                    البيئة: {import.meta.env.DEV ? 'تطوير' : 'إنتاج'}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-gray-800/50 rounded-lg">
                <p className="text-xs text-gray-400">
                  Publisher ID: <span className="text-amber-400">{publisherId}</span>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* اختبار إعلانات البانر */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-amber-400">إعلانات البانر</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-300">بانر علوي</h3>
                  <AdBanner position="top" />
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-300">بانر سفلي</h3>
                  <AdBanner position="bottom" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* اختبار الإعلانات المتجاوبة */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-amber-400">الإعلانات المتجاوبة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-300">إعلان متجاوب في المحتوى</h3>
                  <ResponsiveAd type="inContent" />
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-300">إعلان مقال كبير</h3>
                  <ResponsiveAd type="article" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* اختبار الإعلان المربع */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-amber-400">الإعلان المربع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <AdSquare />
              </div>
            </CardContent>
          </Card>

          {/* اختبار الإعلان المخصص */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-amber-400">إعلان مخصص</CardTitle>
            </CardHeader>
            <CardContent>
              <AdSense
                adSlot="1234567890"
                adFormat="auto"
                style={{
                  display: 'block',
                  width: '100%',
                  height: '200px',
                }}
                placeholderText="إعلان مخصص للاختبار"
              />
            </CardContent>
          </Card>

          {/* معلومات التطوير */}
          {import.meta.env.DEV && (
            <Card className="bg-blue-900/20 border-blue-500/30">
              <CardHeader>
                <CardTitle className="text-blue-400">معلومات التطوير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <p className="text-gray-300">
                    🔧 <strong>وضع التطوير نشط:</strong> الإعلانات معطلة ويتم عرض placeholders
                  </p>
                  <p className="text-gray-300">
                    📝 <strong>للتفعيل:</strong> قم بتحديث Publisher ID وأرقام الوحدات الإعلانية في ملف adsConfig.ts
                  </p>
                  <p className="text-gray-300">
                    🚀 <strong>للنشر:</strong> تأكد من تفعيل الإعلانات في بيئة الإنتاج
                  </p>
                  <p className="text-gray-300">
                    📊 <strong>المراقبة:</strong> راجع وحدة التحكم للحصول على معلومات مفصلة
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdTest;
