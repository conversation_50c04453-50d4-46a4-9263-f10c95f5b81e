# 🎨 دليل إنشاء الوحدات الإعلانية - مرئي ومفصل

## 🚀 البداية: الوصول إلى AdSense

### الخطوة 1: تسجيل الدخول
```
🌐 اذهب إلى: https://www.google.com/adsense/
📧 أدخل بيانات حساب Google الخاص بك
🔑 سجل دخول وانتظر تحميل لوحة التحكم
```

### الخطوة 2: التنقل في لوحة التحكم
```
📊 ستجد في الجانب الأيسر قائمة تحتوي على:
   - نظرة عامة (Overview)
   - الإعلانات (Ads) ← هذا ما نريده
   - التقارير (Reports)
   - المدفوعات (Payments)
   - المواقع (Sites)
```

### الخطوة 3: الوصول إلى قسم الإعلانات
```
📊 اضغط على "الإعلانات" (Ads)
🎯 ستظهر لك خيارات:
   - حسب الموقع (By site)
   - حسب الوحدة الإعلانية (By ad unit) ← اختر هذا
📝 اضغط على "حسب الوحدة الإعلانية"
```

## 🏗️ إنشاء الوحدات الإعلانية

### الوحدة الأولى: البانر العلوي 🔝

#### الخطوة 1: بدء الإنشاء
```
➕ اضغط الزر الأزرق "+" أو "إنشاء وحدة إعلانية جديدة"
📝 ستظهر نافذة جديدة لإعداد الإعلان
```

#### الخطوة 2: الإعدادات الأساسية
```
📝 اسم الوحدة الإعلانية: hodifatech-top-banner
   (يمكنك استخدام أي اسم وصفي)

📐 نوع الإعلان: اختر "إعلان عرض" (Display ad)
   (هذا هو النوع الأكثر شيوعاً)
```

#### الخطوة 3: اختيار الحجم
```
📏 حجم الإعلان: اختر "متجاوب" (Responsive)
   ✅ هذا يجعل الإعلان يتكيف مع جميع أحجام الشاشات

🎨 شكل الإعلان: اختر "أفقي أو مربع" (Horizontal or square)
   ✅ مناسب للبانر العلوي
```

#### الخطوة 4: الإعدادات المتقدمة
```
📱 تحسين للجوال: تأكد من تفعيله ✅
🎯 الاستهداف: اتركه "تلقائي" (Automatic)
🚫 فئات الإعلانات المحظورة: حسب تفضيلك
```

#### الخطوة 5: الإنشاء والحفظ
```
💾 اضغط "إنشاء" (Create)
⏳ انتظر بضع ثوانٍ
📋 ستظهر لك صفحة تحتوي على:
   - كود HTML للإعلان
   - رقم الوحدة الإعلانية (مثل: 1234567890)

📝 انسخ رقم الوحدة الإعلانية واحفظه
```

### الوحدة الثانية: البانر السفلي 🔽

#### كرر نفس خطوات البانر العلوي مع تغيير الاسم:
```
📝 اسم الوحدة: hodifatech-bottom-banner
📐 نوع الإعلان: إعلان عرض (Display ad)
📏 الحجم: متجاوب (Responsive)
🎨 الشكل: أفقي أو مربع (Horizontal or square)
📱 تحسين للجوال: ✅ مفعل
💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة الثالثة: الإعلان المربع 🟦

#### الخطوة 1: بدء الإنشاء
```
➕ اضغط "إنشاء وحدة إعلانية جديدة" مرة أخرى
📝 اسم الوحدة: hodifatech-square-sidebar
📐 نوع الإعلان: إعلان عرض (Display ad)
```

#### الخطوة 2: اختيار حجم ثابت
```
📏 حجم الإعلان: اختر "حجم ثابت" (Fixed size)
📐 من القائمة المنسدلة، اختر:
   "متوسط مستطيل" (Medium Rectangle)
   الأبعاد: 300 × 250 بكسل
```

#### الخطوة 3: الإعدادات والإنشاء
```
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: تلقائي (Automatic)
💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة الرابعة: الإعلان المتجاوب للمحتوى 📄

#### الخطوة 1: الإعدادات الأساسية
```
📝 اسم الوحدة: hodifatech-in-content-responsive
📐 نوع الإعلان: إعلان عرض (Display ad)
📏 الحجم: متجاوب (Responsive)
```

#### الخطوة 2: إعدادات متقدمة للمحتوى
```
🎨 شكل الإعلان: "عمودي أو أفقي" (Vertical or horizontal)
📄 تحسين للمحتوى: ✅ فعل هذا الخيار
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: تلقائي (Automatic)
💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة الخامسة: إعلان المقال الكبير 📰

#### الخطوة 1: الإعدادات الأساسية
```
📝 اسم الوحدة: hodifatech-article-large
📐 نوع الإعلان: إعلان عرض (Display ad)
📏 الحجم: متجاوب (Responsive)
```

#### الخطوة 2: إعدادات متخصصة للمقالات
```
🎨 شكل الإعلان: "عمودي أو أفقي" (Vertical or horizontal)
📄 تحسين للمقالات: ✅ فعل هذا الخيار
🔍 إذا ظهر خيار الكلمات المفتاحية، أضف:
   - تقنية
   - برمجة
   - ويب
   - تطوير
📱 تحسين للجوال: ✅ مفعل
💾 اضغط "إنشاء" واحفظ الرقم
```

## 📋 تجميع المعلومات

### بعد إنشاء جميع الوحدات، ستحصل على:

```
🔝 البانر العلوي: hodifatech-top-banner
   رقم الوحدة: [احفظ الرقم هنا]

🔽 البانر السفلي: hodifatech-bottom-banner
   رقم الوحدة: [احفظ الرقم هنا]

🟦 الإعلان المربع: hodifatech-square-sidebar
   رقم الوحدة: [احفظ الرقم هنا]

📄 الإعلان المتجاوب: hodifatech-in-content-responsive
   رقم الوحدة: [احفظ الرقم هنا]

📰 إعلان المقال: hodifatech-article-large
   رقم الوحدة: [احفظ الرقم هنا]
```

## 🔧 تحديث ملفات المشروع

### الآن استخدم الأرقام التي حصلت عليها:

#### في ملف `src/config/adsConfig.ts`:
```typescript
adUnits: {
  topBanner: {
    adSlot: 'رقم_البانر_العلوي_هنا', // ضع الرقم الحقيقي
    // ...
  },
  bottomBanner: {
    adSlot: 'رقم_البانر_السفلي_هنا', // ضع الرقم الحقيقي
    // ...
  },
  sidebarSquare: {
    adSlot: 'رقم_الإعلان_المربع_هنا', // ضع الرقم الحقيقي
    // ...
  },
  inContentResponsive: {
    adSlot: 'رقم_الإعلان_المتجاوب_هنا', // ضع الرقم الحقيقي
    // ...
  },
  articleLarge: {
    adSlot: 'رقم_إعلان_المقال_هنا', // ضع الرقم الحقيقي
    // ...
  },
}
```

## ✅ التحقق النهائي

### قبل النشر، تأكد من:
```
✅ إنشاء جميع الوحدات الخمس
✅ حفظ جميع أرقام الوحدات
✅ تحديث ملف adsConfig.ts
✅ تحديث معرف الناشر في index.html
✅ اختبار الموقع محلياً
```

### بعد النشر:
```
⏳ انتظر 24-48 ساعة لظهور الإعلانات
📊 راقب لوحة تحكم AdSense
🔍 تحقق من عدم وجود أخطاء
📱 اختبر على أجهزة مختلفة
```

---

**🎉 ممتاز!** الآن لديك دليل شامل ومفصل لإنشاء جميع الوحدات الإعلانية المطلوبة. اتبع الخطوات بعناية وستحصل على نظام إعلانات احترافي في موقعك!
