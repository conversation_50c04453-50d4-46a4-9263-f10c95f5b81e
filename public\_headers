# إعدادات CORS شاملة للنطاق المخصص hodifatech.com

/*
  Access-Control-Allow-Origin: https://hodifatech.com
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin
  Access-Control-Allow-Credentials: true
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

# إعدادات خاصة للـ API Functions
/api/*
  Access-Control-Allow-Origin: https://hodifatech.com
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin
  Access-Control-Allow-Credentials: true

/.netlify/functions/*
  Access-Control-Allow-Origin: https://hodifatech.com
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin
  Access-Control-Allow-Credentials: true

# إعدادات التخزين المؤقت للملفات الثابتة
*.js
  Cache-Control: public, max-age=31536000, immutable

*.css
  Cache-Control: public, max-age=31536000, immutable

*.png
  Cache-Control: public, max-age=31536000, immutable

*.jpg
  Cache-Control: public, max-age=31536000, immutable

*.jpeg
  Cache-Control: public, max-age=31536000, immutable

*.svg
  Cache-Control: public, max-age=31536000, immutable

*.ico
  Cache-Control: public, max-age=31536000, immutable

*.webp
  Cache-Control: public, max-age=31536000, immutable
