import React from 'react';
import AdSense from './AdSense';
import { getAdUnitConfig } from '@/config/adsConfig';

// خصائص مكون الإعلان المربع
interface AdSquareProps {
  className?: string;
  style?: React.CSSProperties;
  showLabel?: boolean;
}

// مكون الإعلان المربع الجانبي
const AdSquare: React.FC<AdSquareProps> = ({
  className = '',
  style = {},
  showLabel = true,
}) => {
  // الحصول على تكوين الإعلان المربع
  const adConfig = getAdUnitConfig('sidebarSquare');

  return (
    <div className={`w-full max-w-sm mx-auto ${className}`}>
      {/* عنوان الإعلان */}
      {showLabel && (
        <div className="text-center mb-2">
          <span className="text-xs text-gray-500 uppercase tracking-wider">
            إعلان
          </span>
        </div>
      )}
      
      {/* الإعلان */}
      <AdSense
        adSlot={adConfig.adSlot}
        adFormat={adConfig.adFormat}
        adLayout={adConfig.adLayout}
        adLayoutKey={adConfig.adLayoutKey}
        style={{
          ...adConfig.style,
          ...style,
        }}
        className="w-full"
        placeholderText="إعلان مربع جانبي"
      />
    </div>
  );
};

export default AdSquare;
