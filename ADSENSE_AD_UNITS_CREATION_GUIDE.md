# 📋 دليل إنشاء الوحدات الإعلانية - خطوة بخطوة

## 🎯 المرحلة الأولى: الوصول إلى AdSense

### 1. تسجيل الدخول
```
🌐 اذهب إلى: https://www.google.com/adsense/
📧 سجل دخول بحساب Google
🏠 انتظر تحميل لوحة التحكم
```

### 2. التنقل إلى قسم الإعلانات
```
📊 من القائمة الجانبية، اضغط "الإعلانات" أو "Ads"
🎯 اختر "حسب الوحدة الإعلانية" أو "By ad unit"
➕ اضغط الزر الأزرق "+" أو "Create new ad unit"
```

## 🏗️ المرحلة الثانية: إنشاء الوحدات (5 وحدات مطلوبة)

### الوحدة 1️⃣: البانر العلوي

#### الخطوة 1: اختيار النوع
```
📝 اسم الوحدة: hodifatech-top-banner
📐 نوع الإعلان: "إعلان عرض" (Display ad)
📏 اختر "متجاوب" (Responsive)
```

#### الخطوة 2: الإعدادات
```
🎨 شكل الإعلان: "أفقي أو مربع" (Horizontal or square)
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: "تلقائي" (Automatic)
```

#### الخطوة 3: الإنشاء والحفظ
```
💾 اضغط "إنشاء" (Create)
📋 انسخ رقم الوحدة الإعلانية (مثل: 1234567890)
📝 احفظه في مكان آمن
```

### الوحدة 2️⃣: البانر السفلي

#### نفس خطوات البانر العلوي مع تغيير الاسم:
```
📝 اسم الوحدة: hodifatech-bottom-banner
📐 نوع الإعلان: "إعلان عرض" (Display ad)
📏 اختر "متجاوب" (Responsive)
🎨 شكل الإعلان: "أفقي أو مربع" (Horizontal or square)

💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة 3️⃣: الإعلان المربع

#### الخطوة 1: اختيار النوع
```
📝 اسم الوحدة: hodifatech-square-sidebar
📐 نوع الإعلان: "إعلان عرض" (Display ad)
📏 اختر "حجم ثابت" (Fixed size)
```

#### الخطوة 2: تحديد الحجم
```
📐 الحجم: "متوسط مستطيل" (Medium Rectangle)
📏 الأبعاد: 300 × 250 بكسل
🎨 مناسب للشريط الجانبي
```

#### الخطوة 3: الإعدادات والإنشاء
```
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: "تلقائي" (Automatic)
💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة 4️⃣: الإعلان المتجاوب للمحتوى

#### الخطوة 1: اختيار النوع
```
📝 اسم الوحدة: hodifatech-in-content-responsive
📐 نوع الإعلان: "إعلان عرض" (Display ad)
📏 اختر "متجاوب" (Responsive)
```

#### الخطوة 2: الإعدادات المتقدمة
```
🎨 شكل الإعلان: "عمودي أو أفقي" (Vertical or horizontal)
📄 تحسين للمحتوى: ✅ مفعل
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: "تلقائي" (Automatic)
```

#### الخطوة 3: الإنشاء
```
💾 اضغط "إنشاء" واحفظ الرقم
```

### الوحدة 5️⃣: إعلان المقال الكبير

#### الخطوة 1: اختيار النوع
```
📝 اسم الوحدة: hodifatech-article-large
📐 نوع الإعلان: "إعلان عرض" (Display ad)
📏 اختر "متجاوب" (Responsive)
```

#### الخطوة 2: الإعدادات المتخصصة
```
🎨 شكل الإعلان: "عمودي أو أفقي" (Vertical or horizontal)
📄 تحسين للمقالات: ✅ مفعل
🔍 كلمات مفتاحية: تقنية، برمجة، ويب، تطوير
📱 تحسين للجوال: ✅ مفعل
🎯 الاستهداف: "تلقائي" (Automatic)
```

#### الخطوة 3: الإنشاء
```
💾 اضغط "إنشاء" واحفظ الرقم
```

## 📝 المرحلة الثالثة: تحديث ملفات المشروع

### 1. تحديث معرف الناشر

#### في ملف `index.html`:
```html
<!-- ابحث عن هذا السطر: -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>

<!-- واستبدله بـ: -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR_ACTUAL_PUBLISHER_ID" crossorigin="anonymous"></script>
```

#### أيضاً في `index.html`:
```html
<!-- ابحث عن هذا السطر: -->
<meta name="google-adsense-account" content="ca-pub-XXXXXXXXXXXXXXXXX">

<!-- واستبدله بـ: -->
<meta name="google-adsense-account" content="ca-pub-YOUR_ACTUAL_PUBLISHER_ID">
```

### 2. تحديث أرقام الوحدات الإعلانية

#### في ملف `src/config/adsConfig.ts`:
```typescript
adUnits: {
  topBanner: {
    adSlot: 'YOUR_TOP_BANNER_AD_SLOT', // ضع رقم البانر العلوي هنا
    // ...
  },
  bottomBanner: {
    adSlot: 'YOUR_BOTTOM_BANNER_AD_SLOT', // ضع رقم البانر السفلي هنا
    // ...
  },
  sidebarSquare: {
    adSlot: 'YOUR_SIDEBAR_SQUARE_AD_SLOT', // ضع رقم الإعلان المربع هنا
    // ...
  },
  inContentResponsive: {
    adSlot: 'YOUR_IN_CONTENT_RESPONSIVE_AD_SLOT', // ضع رقم الإعلان المتجاوب هنا
    // ...
  },
  articleLarge: {
    adSlot: 'YOUR_ARTICLE_LARGE_AD_SLOT', // ضع رقم إعلان المقال هنا
    // ...
  },
}
```

## 🧪 المرحلة الرابعة: الاختبار

### 1. اختبار محلي
```bash
npm run dev
# اذهب إلى: http://localhost:5173/ad-test
```

### 2. فحص الإعدادات
```
✅ تحقق من ظهور placeholders في وضع التطوير
✅ تحقق من عدم وجود أخطاء في Console
✅ تحقق من تحميل سكريبت AdSense
```

### 3. النشر والاختبار النهائي
```bash
npm run build
# ارفع على GitHub/Netlify
# اذهب إلى: https://hodifatech.com/ad-test
```

## ⚠️ نصائح مهمة

### أثناء إنشاء الوحدات:
- ✅ استخدم أسماء وصفية للوحدات
- ✅ اختر الأحجام المناسبة لموقعك
- ✅ فعل تحسين الجوال دائماً
- ✅ استخدم الاستهداف التلقائي في البداية

### بعد الإنشاء:
- ✅ احفظ جميع أرقام الوحدات في مكان آمن
- ✅ لا تشارك أرقام الوحدات علناً
- ✅ اختبر كل وحدة على حدة
- ✅ راقب الأداء بانتظام

## 🔍 استكشاف الأخطاء الشائعة

### الوحدة لا تظهر في القائمة:
```
🔄 انتظر بضع دقائق وحدث الصفحة
🌐 تحقق من اتصال الإنترنت
🔍 تأكد من حفظ الوحدة بنجاح
```

### رسالة خطأ أثناء الإنشاء:
```
📝 تحقق من اسم الوحدة (لا يجب أن يكون مكرراً)
🎯 تأكد من اختيار جميع الإعدادات المطلوبة
🔄 جرب إعادة تحميل الصفحة
```

### الإعلانات لا تظهر بعد النشر:
```
⏳ انتظر 24-48 ساعة للمراجعة
✅ تحقق من موافقة AdSense على الموقع
🔍 تحقق من صحة أرقام الوحدات
📱 اختبر على أجهزة مختلفة
```

---

**🎉 تهانينا!** بعد اتباع هذه الخطوات، ستكون قد أنشأت جميع الوحدات الإعلانية المطلوبة وحدثت ملفات المشروع. موقعك جاهز الآن لبدء عرض الإعلانات وكسب الأرباح!
