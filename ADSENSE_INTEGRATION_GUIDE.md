# دليل دمج Google AdSense مع موقع hodifatech

## 📋 نظرة عامة

تم دمج Google AdSense بنجاح مع موقع hodifatech المبني بـ React + Vite + TypeScript. يتضمن النظام مكونات متقدمة للإعلانات مع دعم التحميل المتأخر ومنع الإعلانات في وضع التطوير.

## 🚀 الميزات المتوفرة

### 1. مكونات الإعلانات
- **AdSense**: المكون الأساسي للإعلانات
- **AdBanner**: إعلانات البانر (علوي/سفلي)
- **AdSquare**: إعلانات مربعة جانبية
- **ResponsiveAd**: إعلانات متجاوبة للمحتوى
- **AdManager**: إدارة عامة للإعلانات

### 2. إدارة متقدمة
- **useAdSense Hook**: إدارة حالة الإعلانات
- **adsConfig**: تكوين شامل للإعلانات
- **Lazy Loading**: تحميل متأخر لتحسين الأداء
- **Error Handling**: معالجة أخطاء التحميل

## ⚙️ التكوين المطلوب

### 1. تحديث معرف الناشر

في ملف `src/config/adsConfig.ts`:
```typescript
publisherId: 'ca-pub-YOUR_ACTUAL_PUBLISHER_ID', // استبدل بمعرفك الحقيقي
```

في ملف `index.html`:
```html
<!-- استبدل ca-pub-XXXXXXXXXXXXXXXXX بمعرفك الحقيقي -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR_ACTUAL_PUBLISHER_ID" crossorigin="anonymous"></script>
<meta name="google-adsense-account" content="ca-pub-YOUR_ACTUAL_PUBLISHER_ID">
```

### 2. تحديث أرقام الوحدات الإعلانية

في ملف `src/config/adsConfig.ts`، استبدل أرقام الوحدات الإعلانية:
```typescript
adUnits: {
  topBanner: {
    adSlot: 'YOUR_TOP_BANNER_AD_SLOT', // استبدل برقم الوحدة الحقيقي
    // ...
  },
  bottomBanner: {
    adSlot: 'YOUR_BOTTOM_BANNER_AD_SLOT', // استبدل برقم الوحدة الحقيقي
    // ...
  },
  // ... باقي الوحدات
}
```

## 📍 أماكن الإعلانات المدمجة

### الصفحة الرئيسية (Index.tsx)
- إعلان بانر علوي بعد Navigation
- إعلان متجاوب في المحتوى
- إعلان بانر سفلي قبل Footer

### صفحة الأخبار التقنية (TechNews.tsx)
- إعلان بانر علوي
- إعلان متجاوب قبل قائمة الأخبار
- إعلان مقال كبير بعد قائمة الأخبار
- إعلان بانر سفلي

## 🛠️ كيفية الاستخدام

### استخدام مكون الإعلان الأساسي
```tsx
import AdSense from '@/components/AdSense';

<AdSense
  adSlot="1234567890"
  adFormat="auto"
  style={{ width: '100%', height: '90px' }}
/>
```

### استخدام إعلان البانر
```tsx
import AdBanner from '@/components/AdBanner';

<AdBanner position="top" className="my-4" />
<AdBanner position="bottom" className="my-4" />
```

### استخدام الإعلان المتجاوب
```tsx
import ResponsiveAd from '@/components/ResponsiveAd';

<ResponsiveAd type="inContent" className="my-6" />
<ResponsiveAd type="article" className="my-6" />
```

### استخدام الإعلان المربع
```tsx
import AdSquare from '@/components/AdSquare';

<AdSquare className="my-4" showLabel={true} />
```

## 🔧 إعدادات متقدمة

### تفعيل/إلغاء الإعلانات حسب البيئة
```typescript
// في adsConfig.ts
environment: {
  development: false, // معطل في التطوير
  production: true,   // مفعل في الإنتاج
}
```

### إعدادات التحميل المتأخر
```typescript
lazyLoading: {
  enabled: true,
  rootMargin: '200px', // تحميل عند 200px من الشاشة
  threshold: 0.1,
}
```

### إعدادات الأداء
```typescript
performance: {
  enableAsyncLoading: true, // التحميل غير المتزامن
  enableLazyLoading: true,  // التحميل المتأخر
  preloadAds: false,        // عدم التحميل المسبق
}
```

## 📊 مراقبة الأداء

### تسجيل الأحداث
```typescript
import { logAdEvent } from '@/hooks/useAdSense';

// تسجيل تحميل الإعلان
logAdEvent('ad_loaded', adSlot);

// تسجيل خطأ في الإعلان
logAdEvent('ad_error', adSlot, { error: errorMessage });
```

### فحص حالة AdSense
```tsx
import { useAdSenseStatus } from '@/hooks/useAdSense';

const { isScriptLoaded, isEnabled, publisherId } = useAdSenseStatus();
```

## 🚫 منع الإعلانات في التطوير

النظام يمنع تلقائياً عرض الإعلانات في وضع التطوير ويعرض placeholders بدلاً منها. هذا يساعد في:
- تجنب النقرات العرضية أثناء التطوير
- تحسين سرعة التطوير
- منع انتهاك سياسات AdSense

## ✅ خطوات ما قبل النشر

1. **تحديث معرف الناشر** في جميع الملفات
2. **تحديث أرقام الوحدات الإعلانية** في adsConfig.ts
3. **اختبار الإعلانات** في بيئة الإنتاج
4. **التأكد من سياسات AdSense** والامتثال لها
5. **مراقبة الأداء** بعد النشر

## 🎯 نصائح لقبول AdSense

### المحتوى
- محتوى أصلي وعالي الجودة
- تحديث منتظم للمحتوى
- مقالات مفيدة ومفصلة

### التصميم
- تصميم احترافي ومتجاوب
- سهولة التنقل
- سرعة تحميل جيدة

### السياسات
- الامتثال لسياسات AdSense
- صفحات الخصوصية وشروط الاستخدام
- محتوى مناسب لجميع الأعمار

## 🔍 استكشاف الأخطاء

### الإعلانات لا تظهر
1. تحقق من معرف الناشر
2. تحقق من أرقام الوحدات الإعلانية
3. تحقق من إعدادات البيئة
4. تحقق من وحدة التحكم للأخطاء

### أخطاء التحميل
1. تحقق من اتصال الإنترنت
2. تحقق من حاجبات الإعلانات
3. تحقق من إعدادات CORS
4. تحقق من سياسات المحتوى

## 📞 الدعم

للمساعدة في تكوين AdSense أو حل المشاكل، يرجى مراجعة:
- [مركز مساعدة Google AdSense](https://support.google.com/adsense)
- [وثائق AdSense للمطورين](https://developers.google.com/adsense)
- ملفات التكوين في المشروع
