import { useEffect, useRef, useState } from 'react';
import { isAdsEnabled, getPublisherId } from '@/config/adsConfig';

// تعريف نوع البيانات للإعلان
interface AdSenseProps {
  adSlot: string;
  adFormat?: string;
  adLayout?: string;
  adLayoutKey?: string;
  style?: React.CSSProperties;
  className?: string;
}

// تعريف نوع البيانات لحالة الإعلان
interface AdState {
  isLoaded: boolean;
  isError: boolean;
  errorMessage?: string;
}

// Hook مخصص لإدارة Google AdSense
export const useAdSense = (props: AdSenseProps) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [adState, setAdState] = useState<AdState>({
    isLoaded: false,
    isError: false,
  });
  const [isVisible, setIsVisible] = useState(false);

  // التحقق من تحميل AdSense script
  const isAdSenseLoaded = () => {
    return typeof window !== 'undefined' && 
           window.adsbygoogle && 
           Array.isArray(window.adsbygoogle);
  };

  // تحميل AdSense script إذا لم يكن محملاً
  const loadAdSenseScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (isAdSenseLoaded()) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${getPublisherId()}`;
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
        console.log('✅ AdSense script loaded successfully');
        resolve();
      };
      
      script.onerror = () => {
        console.error('❌ Failed to load AdSense script');
        reject(new Error('Failed to load AdSense script'));
      };

      document.head.appendChild(script);
    });
  };

  // تهيئة الإعلان
  const initializeAd = async () => {
    if (!isAdsEnabled()) {
      console.log('🚫 Ads disabled in current environment');
      setAdState({
        isLoaded: false,
        isError: true,
        errorMessage: 'Ads disabled in development mode',
      });
      return;
    }

    try {
      // تحميل AdSense script
      await loadAdSenseScript();

      // التأكد من وجود العنصر
      if (!adRef.current) {
        throw new Error('Ad container not found');
      }

      // تهيئة الإعلان
      if (window.adsbygoogle) {
        window.adsbygoogle.push({});
        console.log(`✅ Ad initialized for slot: ${props.adSlot}`);
        
        setAdState({
          isLoaded: true,
          isError: false,
        });
      }
    } catch (error) {
      console.error('❌ Error initializing ad:', error);
      setAdState({
        isLoaded: false,
        isError: true,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Intersection Observer للتحميل المتأخر
  useEffect(() => {
    if (!adRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isVisible) {
            setIsVisible(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '200px',
        threshold: 0.1,
      }
    );

    observer.observe(adRef.current);

    return () => {
      observer.disconnect();
    };
  }, [isVisible]);

  // تهيئة الإعلان عند الرؤية
  useEffect(() => {
    if (isVisible && !adState.isLoaded && !adState.isError) {
      initializeAd();
    }
  }, [isVisible, adState.isLoaded, adState.isError]);

  // إعادة تهيئة الإعلان عند تغيير الخصائص
  useEffect(() => {
    if (isVisible && adState.isLoaded) {
      // إعادة تهيئة الإعلان إذا تغيرت الخصائص
      const timer = setTimeout(() => {
        initializeAd();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [props.adSlot, isVisible]);

  return {
    adRef,
    adState,
    isVisible,
    isAdsEnabled: isAdsEnabled(),
    refreshAd: initializeAd,
  };
};

// Hook للتحقق من حالة AdSense العامة
export const useAdSenseStatus = () => {
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);

  useEffect(() => {
    setIsEnabled(isAdsEnabled());
    setIsScriptLoaded(isAdSenseLoaded());
  }, []);

  return {
    isScriptLoaded,
    isEnabled,
    publisherId: getPublisherId(),
  };
};

// دالة مساعدة لتسجيل أحداث الإعلانات
export const logAdEvent = (event: string, adSlot: string, details?: any) => {
  if (import.meta.env.DEV) {
    console.log(`📊 Ad Event: ${event}`, {
      adSlot,
      timestamp: new Date().toISOString(),
      details,
    });
  }
};

// تعريف نوع البيانات العامة لـ AdSense
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
