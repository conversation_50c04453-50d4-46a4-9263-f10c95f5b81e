#!/usr/bin/env node

/**
 * سكريبت لاختبار الدردشة في بيئة الإنتاج
 * يتحقق من عمل Firebase والدردشة على النطاق المخصص
 */

import https from 'https';
import http from 'http';

// إعدادات الاختبار
const CUSTOM_DOMAIN = 'https://hodifatech.com';
const CHAT_PATH = '/group-chat';

console.log('🔍 بدء اختبار الدردشة في بيئة الإنتاج...\n');

// دالة لإجراء طلب HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.request(url, {
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Production-Chat-Test/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'ar,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Origin': CUSTOM_DOMAIN,
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// اختبار 1: فحص صفحة الدردشة
async function testChatPage() {
  console.log('1️⃣ اختبار صفحة الدردشة...');
  
  const chatUrl = `${CUSTOM_DOMAIN}${CHAT_PATH}`;
  
  try {
    const response = await makeRequest(chatUrl);
    
    if (response.statusCode === 200) {
      console.log('   ✅ صفحة الدردشة تعمل بشكل صحيح');
      console.log(`   📊 Status: ${response.statusCode}`);
      
      // فحص محتوى الصفحة
      const hasFirebaseScript = response.data.includes('firebase');
      const hasChatComponent = response.data.includes('الدردشة الجماعية') || response.data.includes('GroupChat');
      
      if (hasFirebaseScript) {
        console.log('   ✅ Firebase SDK موجود في الصفحة');
      } else {
        console.log('   ⚠️ Firebase SDK قد يكون مفقود');
      }
      
      if (hasChatComponent) {
        console.log('   ✅ مكون الدردشة موجود');
      } else {
        console.log('   ⚠️ مكون الدردشة قد يكون مفقود');
      }
      
    } else if (response.statusCode === 404) {
      console.log('   ❌ صفحة الدردشة غير موجودة (404)');
    } else {
      console.log(`   ❌ خطأ في صفحة الدردشة: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول لصفحة الدردشة: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 2: فحص Firebase Database Rules
async function testFirebaseAccess() {
  console.log('2️⃣ اختبار الوصول لـ Firebase...');
  
  const firebaseUrl = 'https://myprofilewebsitechatproject-default-rtdb.europe-west1.firebasedatabase.app/messages.json';
  
  try {
    const response = await makeRequest(firebaseUrl);
    
    if (response.statusCode === 200) {
      console.log('   ✅ Firebase Database متاح');
      console.log(`   📊 Status: ${response.statusCode}`);
      
      try {
        const data = JSON.parse(response.data);
        if (data === null) {
          console.log('   📝 قاعدة البيانات فارغة (طبيعي للبداية)');
        } else {
          const messageCount = Object.keys(data).length;
          console.log(`   📝 عدد الرسائل الموجودة: ${messageCount}`);
        }
      } catch (parseError) {
        console.log('   ⚠️ البيانات المرجعة غير صالحة');
      }
      
    } else if (response.statusCode === 401) {
      console.log('   ❌ غير مصرح بالوصول لـ Firebase (تحقق من Database Rules)');
    } else if (response.statusCode === 403) {
      console.log('   ❌ ممنوع الوصول لـ Firebase (تحقق من Database Rules)');
    } else {
      console.log(`   ❌ خطأ في الوصول لـ Firebase: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول لـ Firebase: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 3: فحص CORS Headers
async function testCORSHeaders() {
  console.log('3️⃣ اختبار CORS Headers...');
  
  const chatUrl = `${CUSTOM_DOMAIN}${CHAT_PATH}`;
  
  try {
    const response = await makeRequest(chatUrl, {
      method: 'OPTIONS',
      headers: {
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers['access-control-allow-origin'],
      'access-control-allow-methods': response.headers['access-control-allow-methods'],
      'access-control-allow-headers': response.headers['access-control-allow-headers'],
      'access-control-allow-credentials': response.headers['access-control-allow-credentials']
    };
    
    console.log('   📋 CORS Headers:');
    Object.entries(corsHeaders).forEach(([key, value]) => {
      if (value) {
        console.log(`      ${key}: ${value}`);
      }
    });
    
    if (corsHeaders['access-control-allow-origin']) {
      console.log('   ✅ CORS Headers موجودة');
    } else {
      console.log('   ❌ CORS Headers مفقودة');
    }
    
  } catch (error) {
    console.log(`   ❌ فشل في اختبار CORS: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 4: فحص SSL Certificate
async function testSSLCertificate() {
  console.log('4️⃣ اختبار SSL Certificate...');
  
  try {
    const response = await makeRequest(CUSTOM_DOMAIN);
    
    if (response.statusCode === 200) {
      console.log('   ✅ SSL Certificate يعمل بشكل صحيح');
      console.log(`   📊 Status: ${response.statusCode}`);
    } else {
      console.log(`   ⚠️ استجابة غير متوقعة: ${response.statusCode}`);
    }
  } catch (error) {
    if (error.message.includes('certificate')) {
      console.log('   ❌ مشكلة في SSL Certificate');
    } else {
      console.log(`   ❌ خطأ في الاتصال: ${error.message}`);
    }
  }
  
  console.log('');
}

// اختبار 5: فحص سرعة التحميل
async function testLoadSpeed() {
  console.log('5️⃣ اختبار سرعة التحميل...');
  
  const chatUrl = `${CUSTOM_DOMAIN}${CHAT_PATH}`;
  const startTime = Date.now();
  
  try {
    const response = await makeRequest(chatUrl);
    const endTime = Date.now();
    const loadTime = endTime - startTime;
    
    if (response.statusCode === 200) {
      console.log(`   ⏱️ وقت التحميل: ${loadTime}ms`);
      
      if (loadTime < 1000) {
        console.log('   ✅ سرعة تحميل ممتازة');
      } else if (loadTime < 3000) {
        console.log('   ⚠️ سرعة تحميل مقبولة');
      } else {
        console.log('   ❌ سرعة تحميل بطيئة');
      }
    }
  } catch (error) {
    console.log(`   ❌ فشل في قياس سرعة التحميل: ${error.message}`);
  }
  
  console.log('');
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log(`🌐 اختبار الدردشة في الإنتاج: ${CUSTOM_DOMAIN}${CHAT_PATH}`);
  console.log('=' .repeat(60));
  console.log('');
  
  await testChatPage();
  await testFirebaseAccess();
  await testCORSHeaders();
  await testSSLCertificate();
  await testLoadSpeed();
  
  console.log('🎉 انتهاء اختبارات الإنتاج!');
  console.log('');
  console.log('📋 التوصيات للإنتاج:');
  console.log('   1. تأكد من إضافة النطاق في Firebase Console');
  console.log('   2. راجع Database Rules في Firebase');
  console.log('   3. تحقق من SSL Certificate في Netlify');
  console.log('   4. راقب Performance في Netlify Analytics');
  console.log('   5. اختبر الدردشة يدوياً من المتصفح');
  console.log('');
  console.log('🔗 روابط مفيدة:');
  console.log(`   - الدردشة: ${CUSTOM_DOMAIN}${CHAT_PATH}`);
  console.log('   - Firebase Console: https://console.firebase.google.com/project/myprofilewebsitechatproject');
  console.log('   - Netlify Dashboard: https://app.netlify.com');
}

// تشغيل الاختبارات
runAllTests().catch(console.error);
