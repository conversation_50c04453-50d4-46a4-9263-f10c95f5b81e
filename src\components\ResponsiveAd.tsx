import React from 'react';
import AdSense from './AdSense';
import { getAdUnitConfig } from '@/config/adsConfig';

// خصائص مكون الإعلان المتجاوب
interface ResponsiveAdProps {
  type?: 'inContent' | 'article';
  className?: string;
  style?: React.CSSProperties;
  showLabel?: boolean;
}

// مكون الإعلان المتجاوب
const ResponsiveAd: React.FC<ResponsiveAdProps> = ({
  type = 'inContent',
  className = '',
  style = {},
  showLabel = true,
}) => {
  // الحصول على تكوين الإعلان حسب النوع
  const adConfig = type === 'inContent' 
    ? getAdUnitConfig('inContentResponsive')
    : getAdUnitConfig('articleLarge');

  return (
    <div className={`w-full my-6 ${className}`}>
      {/* عنوان الإعلان */}
      {showLabel && (
        <div className="text-center mb-3">
          <span className="text-xs text-gray-500 uppercase tracking-wider">
            إعلان
          </span>
        </div>
      )}
      
      {/* الإعلان */}
      <AdSense
        adSlot={adConfig.adSlot}
        adFormat={adConfig.adFormat}
        adLayout={adConfig.adLayout}
        adLayoutKey={adConfig.adLayoutKey}
        style={{
          ...adConfig.style,
          ...style,
        }}
        className="w-full"
        placeholderText={`إعلان ${type === 'inContent' ? 'متجاوب' : 'مقال كبير'}`}
      />
    </div>
  );
};

export default ResponsiveAd;
