import React from 'react';
import { useAdSense, logAdEvent } from '@/hooks/useAdSense';
import { getPublisherId } from '@/config/adsConfig';
import { AlertCircle, Eye, EyeOff } from 'lucide-react';

// تعريف خصائص مكون الإعلان
interface AdSenseProps {
  adSlot: string;
  adFormat?: string;
  adLayout?: string;
  adLayoutKey?: string;
  style?: React.CSSProperties;
  className?: string;
  showPlaceholder?: boolean;
  placeholderText?: string;
}

// مكون الإعلان الأساسي
const AdSense: React.FC<AdSenseProps> = ({
  adSlot,
  adFormat = 'auto',
  adLayout = '',
  adLayoutKey = '',
  style = {},
  className = '',
  showPlaceholder = true,
  placeholderText = 'إعلان',
}) => {
  const { adRef, adState, isVisible, isAdsEnabled, refreshAd } = useAdSense({
    adSlot,
    adFormat,
    adLayout,
    adLayoutKey,
    style,
    className,
  });

  // تسجيل أحداث الإعلان
  React.useEffect(() => {
    if (adState.isLoaded) {
      logAdEvent('ad_loaded', adSlot);
    }
    if (adState.isError) {
      logAdEvent('ad_error', adSlot, { error: adState.errorMessage });
    }
  }, [adState, adSlot]);

  // إذا كانت الإعلانات معطلة، عرض placeholder في وضع التطوير
  if (!isAdsEnabled) {
    if (import.meta.env.DEV && showPlaceholder) {
      return (
        <div
          className={`bg-gray-800/50 border-2 border-dashed border-gray-600 rounded-lg p-4 text-center ${className}`}
          style={style}
        >
          <div className="flex items-center justify-center space-x-2 space-x-reverse text-gray-400">
            <EyeOff className="w-5 h-5" />
            <span className="text-sm font-medium">
              {placeholderText} (معطل في وضع التطوير)
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-2">
            Ad Slot: {adSlot}
          </div>
        </div>
      );
    }
    return null;
  }

  // عرض رسالة خطأ إذا فشل تحميل الإعلان
  if (adState.isError) {
    return (
      <div
        className={`bg-red-900/20 border border-red-500/30 rounded-lg p-4 text-center ${className}`}
        style={style}
      >
        <div className="flex items-center justify-center space-x-2 space-x-reverse text-red-400">
          <AlertCircle className="w-5 h-5" />
          <span className="text-sm font-medium">خطأ في تحميل الإعلان</span>
        </div>
        {import.meta.env.DEV && (
          <div className="text-xs text-red-300 mt-2">
            {adState.errorMessage}
          </div>
        )}
        <button
          onClick={refreshAd}
          className="mt-2 text-xs text-red-300 hover:text-red-200 underline"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  // عرض placeholder أثناء التحميل
  if (!isVisible || !adState.isLoaded) {
    return (
      <div
        ref={adRef}
        className={`bg-gray-900/30 border border-gray-700/50 rounded-lg p-4 text-center animate-pulse ${className}`}
        style={style}
      >
        <div className="flex items-center justify-center space-x-2 space-x-reverse text-gray-500">
          <Eye className="w-5 h-5 animate-pulse" />
          <span className="text-sm font-medium">جاري تحميل الإعلان...</span>
        </div>
      </div>
    );
  }

  // عرض الإعلان الفعلي
  return (
    <div
      ref={adRef}
      className={`adsense-container ${className}`}
      style={style}
    >
      <ins
        className="adsbygoogle"
        style={{
          display: 'block',
          ...style,
        }}
        data-ad-client={getPublisherId()}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-ad-layout={adLayout}
        data-ad-layout-key={adLayoutKey}
        data-full-width-responsive="true"
      />
    </div>
  );
};

export default AdSense;
