// تكوين إعلانات Google AdSense
export const adsConfig = {
  // معرف الناشر (Publisher ID) - يجب استبداله بمعرفك الحقيقي
  publisherId: 'ca-pub-8193107773366895', // استبدل هذا بمعرف AdSense الخاص بك
  
  // إعدادات البيئة
  environment: {
    development: false, // منع الإعلانات في وضع التطوير
    production: true,   // تفعيل الإعلانات في الإنتاج
  },
  
  // أنواع الإعلانات المختلفة
  adUnits: {
    // إعلان البانر العلوي
    topBanner: {
      adSlot: '1234567890', // استبدل برقم الوحدة الإعلانية
      adFormat: 'auto',
      adLayout: '',
      adLayoutKey: '',
      style: {
        display: 'block',
        width: '100%',
        height: '90px',
      },
    },
    
    // إعلان البانر السفلي
    bottomBanner: {
      adSlot: '1234567891', // استبدل برقم الوحدة الإعلانية
      adFormat: 'auto',
      adLayout: '',
      adLayoutKey: '',
      style: {
        display: 'block',
        width: '100%',
        height: '90px',
      },
    },
    
    // إعلان مربع جانبي
    sidebarSquare: {
      adSlot: '1234567892', // استبدل برقم الوحدة الإعلانية
      adFormat: 'auto',
      adLayout: '',
      adLayoutKey: '',
      style: {
        display: 'block',
        width: '300px',
        height: '250px',
      },
    },
    
    // إعلان متجاوب في المحتوى
    inContentResponsive: {
      adSlot: '1234567893', // استبدل برقم الوحدة الإعلانية
      adFormat: 'auto',
      adLayout: '',
      adLayoutKey: '',
      style: {
        display: 'block',
        width: '100%',
        height: '280px',
      },
    },
    
    // إعلان مقال كبير
    articleLarge: {
      adSlot: '1234567894', // استبدل برقم الوحدة الإعلانية
      adFormat: 'auto',
      adLayout: '',
      adLayoutKey: '',
      style: {
        display: 'block',
        width: '100%',
        height: '320px',
      },
    },
  },
  
  // إعدادات التحميل المتأخر (Lazy Loading)
  lazyLoading: {
    enabled: true,
    rootMargin: '200px', // تحميل الإعلان عندما يكون على بعد 200px من الشاشة
    threshold: 0.1,
  },
  
  // إعدادات التتبع والتحليلات
  analytics: {
    trackImpressions: true,
    trackClicks: true,
    trackErrors: true,
  },
  
  // إعدادات الأمان والخصوصية
  privacy: {
    enableConsentMode: true, // تفعيل وضع الموافقة للخصوصية
    enablePersonalizedAds: true, // الإعلانات المخصصة
  },
  
  // إعدادات الأداء
  performance: {
    enableAsyncLoading: true, // التحميل غير المتزامن
    enableLazyLoading: true,  // التحميل المتأخر
    preloadAds: false,        // عدم التحميل المسبق لتوفير البيانات
  },
};

// دالة للحصول على معرف الناشر
export const getPublisherId = (): string => {
  return adsConfig.publisherId;
};

// دالة للتحقق من تفعيل الإعلانات حسب البيئة
export const isAdsEnabled = (): boolean => {
  const isDevelopment = import.meta.env.DEV;
  return isDevelopment ? adsConfig.environment.development : adsConfig.environment.production;
};

// دالة للحصول على تكوين وحدة إعلانية محددة
export const getAdUnitConfig = (adUnitName: keyof typeof adsConfig.adUnits) => {
  return adsConfig.adUnits[adUnitName];
};

// دالة للحصول على جميع أرقام الوحدات الإعلانية
export const getAllAdSlots = (): string[] => {
  return Object.values(adsConfig.adUnits).map(unit => unit.adSlot);
};
