import React, { useEffect } from 'react';
import { useAdSenseStatus } from '@/hooks/useAdSense';
import { isAdsEnabled } from '@/config/adsConfig';

// مكون إدارة الإعلانات العام
const AdManager: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isScriptLoaded, isEnabled, publisherId } = useAdSenseStatus();

  useEffect(() => {
    // تسجيل حالة الإعلانات عند التحميل
    console.log('🎯 AdSense Manager Status:', {
      isEnabled,
      isScriptLoaded,
      publisherId,
      environment: import.meta.env.DEV ? 'development' : 'production',
    });

    // إضافة معلومات إضافية في وضع التطوير
    if (import.meta.env.DEV) {
      console.log('📋 AdSense Development Info:', {
        message: 'الإعلانات معطلة في وضع التطوير',
        note: 'سيتم عرض placeholders بدلاً من الإعلانات الحقيقية',
        reminder: 'تأكد من تحديث Publisher ID في ملف adsConfig.ts',
      });
    }
  }, [isEnabled, isScriptLoaded, publisherId]);

  return <>{children}</>;
};

export default AdManager;
