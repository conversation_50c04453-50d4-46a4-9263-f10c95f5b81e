
import React from "react";
import { ExternalLink, Clock, Globe, RefreshCw, AlertCircle } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import AdBanner from "@/components/AdBanner";
import ResponsiveAd from "@/components/ResponsiveAd";
import AdSquare from "@/components/AdSquare";

// بيانات أخبار تقنية محدثة للعرض (حل مؤقت لمشكلة API)
const staticTechNews = [
  {
    article_id: "1",
    title: "ChatGPT-5 يحدث ثورة في الذكاء الاصطناعي لعام 2025",
    description: "OpenAI تكشف عن ChatGPT-5 بقدرات متقدمة في الفهم والتفكير المنطقي، مما يفتح آفاقاً جديدة في تطوير البرمجيات والأتمتة الذكية.",
    content: "الإصدار الجديد يتميز بقدرات محسنة في البرمجة والتحليل المنطقي، مع دعم أفضل للغات البرمجة المتعددة وحل المشاكل المعقدة.",
    link: "https://openai.com/chatgpt",
    image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
    source_id: "OpenAI Blog",
    category: ["ذكاء اصطناعي", "تقنية"],
    pubDate: "2025-01-15T10:30:00Z"
  },
  {
    article_id: "2",
    title: "React 19 يجلب ميزات ثورية لتطوير الواجهات الحديثة",
    description: "الإصدار الجديد من React يقدم Server Components محسنة، وCompiler جديد، وميزات متقدمة تسهل على المطورين بناء تطبيقات ويب عالية الأداء.",
    content: "React 19 يأتي بميزات مثل React Compiler الجديد، وتحسينات في Concurrent Features، وأدوات تطوير محسنة لتجربة مطور أفضل.",
    link: "https://react.dev",
    image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
    source_id: "React Blog",
    category: ["تطوير ويب", "React"],
    pubDate: "2025-01-14T14:20:00Z"
  },
  {
    article_id: "3",
    title: "الأمن السيبراني في عصر الذكاء الاصطناعي والحوسبة الكمية",
    description: "مع ظهور تقنيات الذكاء الاصطناعي والحوسبة الكمية، تواجه الشركات تحديات أمنية جديدة تتطلب استراتيجيات حماية متطورة ومبتكرة.",
    content: "تقنيات الأمان الجديدة تستخدم الذكاء الاصطناعي لحماية البيانات من التهديدات السيبرانية المتطورة والهجمات الكمية المستقبلية.",
    link: "https://www.cybersecurity-insiders.com",
    image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
    source_id: "Cybersecurity Today",
    category: ["أمن المعلومات", "ذكاء اصطناعي"],
    pubDate: "2025-01-13T09:15:00Z"
  },
  {
    article_id: "4",
    title: "Flutter 3.27 يجلب تحسينات ثورية لتطوير التطبيقات متعددة المنصات",
    description: "الإصدار الجديد من Flutter يقدم أداءً محسناً، وأدوات تطوير متقدمة، ودعماً أفضل لـ Material Design 3 وتطبيقات سطح المكتب.",
    content: "Flutter 3.27 يتميز بتحسينات في الأداء، ودعم محسن للرسوم المتحركة، وأدوات جديدة لتطوير تطبيقات الويب وسطح المكتب بجودة عالية.",
    link: "https://flutter.dev",
    image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
    source_id: "Flutter Team",
    category: ["تطوير تطبيقات", "Flutter"],
    pubDate: "2025-01-12T16:45:00Z"
  },
  {
    article_id: "5",
    title: "ثورة قواعد البيانات: Vector Databases والذكاء الاصطناعي",
    description: "قواعد البيانات الشعاعية تصبح العمود الفقري لتطبيقات الذكاء الاصطناعي الحديثة، مع دعم متقدم للبحث الدلالي والتعلم الآلي.",
    content: "Pinecone وWeaviate وChroma تقود ثورة في تخزين ومعالجة البيانات الشعاعية، مما يمكن تطبيقات الذكاء الاصطناعي من أداء أفضل.",
    link: "https://www.pinecone.io",
    image_url: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop",
    source_id: "AI Database Weekly",
    category: ["قواعد البيانات", "ذكاء اصطناعي"],
    pubDate: "2025-01-11T11:30:00Z"
  },
  {
    article_id: "6",
    title: "WebAssembly 2025: مستقبل الحوسبة عالية الأداء في المتصفحات",
    description: "WebAssembly يتطور ليدعم المزيد من اللغات والميزات، مع إمكانيات جديدة للحوسبة المتوازية والذكاء الاصطناعي في المتصفحات.",
    content: "مع دعم WASI والمزيد من اللغات، يمكن للمطورين الآن تشغيل تطبيقات معقدة بأداء قريب من الأداء الأصلي مباشرة في المتصفح.",
    link: "https://webassembly.org",
    image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
    source_id: "WebAssembly Foundation",
    category: ["تطوير ويب", "WebAssembly"],
    pubDate: "2025-01-10T13:20:00Z"
  },
  {
    article_id: "7",
    title: "TypeScript 5.7: ميزات جديدة تحسن تجربة المطورين",
    description: "الإصدار الجديد من TypeScript يجلب تحسينات في الأداء، ونظام أنواع محسن، وأدوات تطوير أفضل لمشاريع JavaScript الكبيرة.",
    content: "TypeScript 5.7 يقدم ميزات مثل Decorators المحسنة، وتحسينات في Type Inference، ودعم أفضل للمشاريع الكبيرة.",
    link: "https://www.typescriptlang.org",
    image_url: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=500&h=300&fit=crop",
    source_id: "TypeScript Team",
    category: ["تطوير ويب", "TypeScript"],
    pubDate: "2025-01-09T08:15:00Z"
  },
  {
    article_id: "8",
    title: "الحوسبة الكمية تقترب من التطبيقات العملية",
    description: "شركات التقنية الكبرى تحرز تقدماً كبيراً في تطوير حاسوبات كمية قادرة على حل مشاكل معقدة في الذكاء الاصطناعي والتشفير.",
    content: "IBM وGoogle وMicrosoft تتسابق لتطوير حاسوبات كمية عملية قادرة على تغيير مجالات الطب والمالية والأمن السيبراني.",
    link: "https://www.ibm.com/quantum",
    image_url: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=500&h=300&fit=crop",
    source_id: "Quantum Computing Report",
    category: ["حوسبة كمية", "تقنية متقدمة"],
    pubDate: "2025-01-08T15:30:00Z"
  },
  {
    article_id: "9",
    title: "Next.js 15: تطوير تطبيقات الويب بأداء فائق",
    description: "الإصدار الجديد من Next.js يقدم تحسينات جذرية في الأداء، ونظام تخزين مؤقت محسن، وميزات جديدة لتطوير التطبيقات الحديثة.",
    content: "Next.js 15 يتميز بـ App Router المحسن، وTurbopack للبناء السريع، ودعم محسن للـ Server Components والـ Streaming.",
    link: "https://nextjs.org",
    image_url: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=500&h=300&fit=crop",
    source_id: "Vercel Blog",
    category: ["تطوير ويب", "Next.js"],
    pubDate: "2025-01-07T12:45:00Z"
  }
];

// دالة لجلب الأخبار الحقيقية من Netlify Function (مجاني 100%)
const fetchTechNews = async () => {
  try {
    console.log('🔄 Fetching tech news from Netlify Function...');

    // استخدام Netlify Function المحدثة (مجاني 100%)
    const functionUrl = '/.netlify/functions/news';

    const response = await fetch(functionUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.articles && data.articles.length > 0) {
        console.log(`✅ Successfully fetched ${data.articles.length} articles from Netlify Function`);
        console.log(`📡 Source: ${data.source}`);
        return data.articles;
      } else {
        console.warn('⚠️ Netlify Function returned no articles or failed');
        throw new Error('No articles returned from Netlify Function');
      }
    } else {
      console.warn(`❌ Netlify Function failed with status: ${response.status}`);
      throw new Error(`Netlify Function failed: ${response.status}`);
    }

  } catch (error) {
    console.error('❌ Error in fetchTechNews:', error);
    // في حالة الخطأ، استخدم البيانات الثابتة المحدثة
    console.log('📰 Using static fallback due to error');
    return staticTechNews;
  }
};

const TechNews = () => {
  // استخدام React Query لجلب البيانات
  const {
    data: news = [],
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['techNews'],
    queryFn: fetchTechNews,
    staleTime: 5 * 60 * 1000, // 5 دقائق
    gcTime: 10 * 60 * 1000, // 10 دقائق (تم تغيير cacheTime إلى gcTime)
    retry: 3,
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const formatDate = (dateString: string) => {
    if (!dateString) return 'تاريخ غير محدد';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

      return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return 'تاريخ غير صحيح';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      "technology": "bg-blue-500/20 text-blue-400",
      "تقنية": "bg-blue-500/20 text-blue-400",
      "ذكاء اصطناعي": "bg-purple-500/20 text-purple-400",
      "تطوير ويب": "bg-blue-500/20 text-blue-400",
      "قواعد البيانات": "bg-green-500/20 text-green-400",
      "تطوير واجهات": "bg-cyan-500/20 text-cyan-400",
      "أمن المعلومات": "bg-red-500/20 text-red-400",
      "اتجاهات تقنية": "bg-amber-500/20 text-amber-400",
      "science": "bg-green-500/20 text-green-400",
      "business": "bg-orange-500/20 text-orange-400",
      "entertainment": "bg-pink-500/20 text-pink-400",
      "health": "bg-emerald-500/20 text-emerald-400",
      "sports": "bg-indigo-500/20 text-indigo-400",
      "politics": "bg-slate-500/20 text-slate-400"
    };
    return colors[category?.toLowerCase()] || "bg-gray-500/20 text-gray-400";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* إعلان بانر علوي */}
          <AdBanner position="top" className="mb-8" />

          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الأخبار</span> التقنية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-6">
              آخر الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات
            </p>

            {/* ملاحظة حول مصدر الأخبار */}
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center mb-2">
                <Globe className="w-5 h-5 text-blue-400 ml-2" />
                <span className="text-blue-400 font-semibold">� أخبار تقنية محدثة لعام 2025</span>
              </div>
              <p className="text-sm text-gray-300 leading-relaxed">
                ✅ أحدث الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات من مصادر عالمية موثوقة
                <br />
                🔄 يتم تحديث المحتوى بانتظام لضمان الحصول على أحدث المعلومات التقنية
              </p>
            </div>
          </div>

          {/* زر إعادة التحميل */}
          <div className="flex justify-center mb-8">
            <Button
              onClick={() => refetch()}
              disabled={isLoading}
              className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-6 py-2 rounded-full transition-all duration-300 transform hover:scale-105"
            >
              <RefreshCw className={`w-4 h-4 ml-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'جاري التحميل...' : 'تحديث الأخبار'}
            </Button>
          </div>

          {isError ? (
            <div className="text-center py-12">
              <Card className="bg-red-900/20 border-red-500/20 max-w-md mx-auto">
                <CardContent className="p-8">
                  <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-red-400 mb-2">خطأ في تحميل الأخبار</h3>
                  <p className="text-gray-300 mb-4">
                    عذراً، حدث خطأ أثناء تحميل الأخبار. يرجى المحاولة مرة أخرى.
                  </p>
                  <Button
                    onClick={() => refetch()}
                    className="bg-red-500 hover:bg-red-600 text-white"
                  >
                    إعادة المحاولة
                  </Button>
                </CardContent>
              </Card>
            </div>
          ) : isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="bg-gray-900/50 border-amber-500/20 animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-3/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : news.length === 0 ? (
            <div className="text-center py-12">
              <Card className="bg-gray-900/30 border-amber-500/20 max-w-md mx-auto">
                <CardContent className="p-8">
                  <Globe className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-amber-400 mb-2">لا توجد أخبار متاحة</h3>
                  <p className="text-gray-300">
                    لم يتم العثور على أخبار تقنية حالياً. يرجى المحاولة لاحقاً.
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <>
              {/* إعلان متجاوب قبل الأخبار */}
              <ResponsiveAd type="inContent" className="mb-8" />

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {news.map((article: any, index: number) => (
                <Card key={article.article_id || index} className="bg-gray-900/50 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 group">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(article.category?.[0] || 'تقنية')}`}>
                          {article.category?.[0] || 'تقنية'}
                        </span>
                        {/* مؤشر اللغة */}
                        {article.language && (
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            article.language === 'ar'
                              ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                              : 'bg-blue-900/30 text-blue-400 border border-blue-500/30'
                          }`}>
                            {article.language === 'ar' ? 'عربي' : 'EN'}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-400">
                        <Globe className="w-3 h-3 ml-1" />
                        {article.source_id || 'مصدر غير معروف'}
                      </div>
                    </div>
                    <CardTitle className="text-lg text-amber-400 group-hover:text-amber-300 transition-colors line-clamp-2">
                      {article.title || 'عنوان غير متوفر'}
                    </CardTitle>
                  </CardHeader>

                  <CardContent>
                    {/* عرض الصورة إذا كانت متوفرة */}
                    {article.image_url && (
                      <div className="mb-4 rounded-lg overflow-hidden">
                        <img
                          src={article.image_url}
                          alt={article.title}
                          className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-105"
                          onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      </div>
                    )}

                    <p className="text-gray-300 text-sm mb-4 line-clamp-3 leading-relaxed">
                      {article.description || article.content || 'لا يوجد وصف متاح'}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-xs text-gray-400">
                        <Clock className="w-3 h-3 ml-1" />
                        {formatDate(article.pubDate)}
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-amber-400 hover:text-amber-300 hover:bg-amber-500/10 transition-all duration-300"
                        asChild
                      >
                        <a href={article.link} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 ml-1" />
                          اقرأ المزيد
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              </div>

              {/* إعلان في نهاية قائمة الأخبار */}
              <ResponsiveAd type="article" className="mt-8" />
            </>
          )}

          {/* معلومات إضافية */}
          <div className="mt-16">
            <Card className="bg-gray-900/30 border-amber-500/20 max-w-2xl mx-auto">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-amber-400 mb-4">🚀 تقنيتي في الأخبار المباشرة</h3>
                <p className="text-gray-300 mb-6">
                  ما أقدمه لكم :
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
                    <div className="text-green-400 font-semibold mb-1">🌐 مصادر حقيقية</div>
                    <div className="text-gray-300">من مواقع عالمية موثوقة</div>
                  </div>
                  <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                    <div className="text-blue-400 font-semibold mb-1">⚡ المزامنة الفورية</div>
                    <div className="text-gray-300">في تقديم كل ماهو حديث</div>
                  </div>
                  <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-3">
                    <div className="text-purple-400 font-semibold mb-1">🔄 تحديث تلقائي</div>
                    <div className="text-gray-300">أخبار محدثة كل 5 دقائق</div>
                  </div>
                  <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-3">
                    <div className="text-orange-400 font-semibold mb-1">🎯 هدفي</div>
                    <div className="text-gray-300">ضمان ايصال الفائدة للعامة </div>
                  </div>
                </div>
                <div className="mt-4 text-xs text-gray-400">
                  * 🌟 اسعى دائما لنشر الوعي بين عامة المجتمع لذا اشكركم على اهتمامكم 🙏
                </div>
              </CardContent>
            </Card>
          </div>

          {/* إعلان بانر سفلي */}
          <AdBanner position="bottom" className="mt-8" />
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TechNews;
