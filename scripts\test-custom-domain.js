#!/usr/bin/env node

/**
 * سكريبت لاختبار تكوين النطاق المخصص
 * يتحقق من عمل الأخبار والدردشة مع النطاق المخصص
 */

import https from 'https';
import http from 'http';

// إعدادات الاختبار
const CUSTOM_DOMAIN = 'https://hodifatech.com';
const NETLIFY_DOMAIN = 'https://hodifa-tech-profile-main.netlify.app';

console.log('🔍 بدء اختبار النطاق المخصص...\n');

// دالة لإجراء طلب HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.request(url, {
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Custom-Domain-Test/1.0',
        'Accept': 'application/json',
        'Origin': CUSTOM_DOMAIN,
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// اختبار 1: فحص الصفحة الرئيسية
async function testHomePage() {
  console.log('1️⃣ اختبار الصفحة الرئيسية...');
  
  try {
    const response = await makeRequest(CUSTOM_DOMAIN);
    
    if (response.statusCode === 200) {
      console.log('   ✅ الصفحة الرئيسية تعمل بشكل صحيح');
      console.log(`   📊 Status: ${response.statusCode}`);
    } else {
      console.log(`   ❌ خطأ في الصفحة الرئيسية: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول للصفحة الرئيسية: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 2: فحص Netlify Function للأخبار
async function testNewsAPI() {
  console.log('2️⃣ اختبار API الأخبار...');
  
  const newsUrl = `${CUSTOM_DOMAIN}/.netlify/functions/news`;
  
  try {
    const response = await makeRequest(newsUrl);
    
    if (response.statusCode === 200) {
      try {
        const data = JSON.parse(response.data);
        console.log('   ✅ API الأخبار يعمل بشكل صحيح');
        console.log(`   📊 Status: ${response.statusCode}`);
        console.log(`   📰 عدد المقالات: ${data.articles ? data.articles.length : 0}`);
        console.log(`   🔗 المصدر: ${data.source || 'غير محدد'}`);
      } catch (parseError) {
        console.log('   ⚠️ API الأخبار يعمل لكن البيانات غير صحيحة');
      }
    } else {
      console.log(`   ❌ خطأ في API الأخبار: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول لـ API الأخبار: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 3: فحص CORS Headers
async function testCORSHeaders() {
  console.log('3️⃣ اختبار CORS Headers...');
  
  const newsUrl = `${CUSTOM_DOMAIN}/.netlify/functions/news`;
  
  try {
    const response = await makeRequest(newsUrl, {
      method: 'OPTIONS',
      headers: {
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers['access-control-allow-origin'],
      'access-control-allow-methods': response.headers['access-control-allow-methods'],
      'access-control-allow-headers': response.headers['access-control-allow-headers'],
      'access-control-allow-credentials': response.headers['access-control-allow-credentials']
    };
    
    console.log('   📋 CORS Headers:');
    Object.entries(corsHeaders).forEach(([key, value]) => {
      if (value) {
        console.log(`      ${key}: ${value}`);
      }
    });
    
    if (corsHeaders['access-control-allow-origin']) {
      console.log('   ✅ CORS Headers موجودة');
    } else {
      console.log('   ❌ CORS Headers مفقودة');
    }
    
  } catch (error) {
    console.log(`   ❌ فشل في اختبار CORS: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 4: فحص صفحة الدردشة
async function testChatPage() {
  console.log('4️⃣ اختبار صفحة الدردشة...');
  
  const chatUrl = `${CUSTOM_DOMAIN}/group-chat`;
  
  try {
    const response = await makeRequest(chatUrl);
    
    if (response.statusCode === 200) {
      console.log('   ✅ صفحة الدردشة تعمل بشكل صحيح');
      console.log(`   📊 Status: ${response.statusCode}`);
    } else {
      console.log(`   ❌ خطأ في صفحة الدردشة: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول لصفحة الدردشة: ${error.message}`);
  }
  
  console.log('');
}

// اختبار 5: فحص صفحة الأخبار
async function testNewsPage() {
  console.log('5️⃣ اختبار صفحة الأخبار...');
  
  const newsPageUrl = `${CUSTOM_DOMAIN}/tech-news`;
  
  try {
    const response = await makeRequest(newsPageUrl);
    
    if (response.statusCode === 200) {
      console.log('   ✅ صفحة الأخبار تعمل بشكل صحيح');
      console.log(`   📊 Status: ${response.statusCode}`);
    } else {
      console.log(`   ❌ خطأ في صفحة الأخبار: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ فشل في الوصول لصفحة الأخبار: ${error.message}`);
  }
  
  console.log('');
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log(`🌐 اختبار النطاق المخصص: ${CUSTOM_DOMAIN}`);
  console.log('=' .repeat(50));
  console.log('');
  
  await testHomePage();
  await testNewsAPI();
  await testCORSHeaders();
  await testChatPage();
  await testNewsPage();
  
  console.log('🎉 انتهاء الاختبارات!');
  console.log('');
  console.log('📋 التوصيات:');
  console.log('   1. تأكد من إضافة النطاق في Firebase Console');
  console.log('   2. تحقق من إعدادات DNS');
  console.log('   3. تأكد من تفعيل SSL في Netlify');
  console.log('   4. راجع Environment Variables في Netlify');
}

// تشغيل الاختبارات
runAllTests().catch(console.error);
