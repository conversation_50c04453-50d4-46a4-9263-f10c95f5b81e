import React from 'react';
import AdSense from './AdSense';
import { getAdUnitConfig } from '@/config/adsConfig';

// خصائص مكون إعلان البانر
interface AdBannerProps {
  position?: 'top' | 'bottom';
  className?: string;
  style?: React.CSSProperties;
}

// مكون إعلان البانر
const AdBanner: React.FC<AdBannerProps> = ({
  position = 'top',
  className = '',
  style = {},
}) => {
  // الحصول على تكوين الإعلان حسب الموضع
  const adConfig = position === 'top' 
    ? getAdUnitConfig('topBanner')
    : getAdUnitConfig('bottomBanner');

  return (
    <div className={`w-full my-4 ${className}`}>
      {/* عنوان الإعلان */}
      <div className="text-center mb-2">
        <span className="text-xs text-gray-500 uppercase tracking-wider">
          إعلان
        </span>
      </div>
      
      {/* الإعلان */}
      <AdSense
        adSlot={adConfig.adSlot}
        adFormat={adConfig.adFormat}
        adLayout={adConfig.adLayout}
        adLayoutKey={adConfig.adLayoutKey}
        style={{
          ...adConfig.style,
          ...style,
        }}
        className="w-full"
        placeholderText={`إعلان بانر ${position === 'top' ? 'علوي' : 'سفلي'}`}
      />
    </div>
  );
};

export default AdBanner;
