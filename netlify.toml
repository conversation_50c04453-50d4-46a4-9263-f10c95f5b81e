[build]
  publish = "dist"
  command = "npm ci && npm run build"

[build.environment]
  NODE_VERSION = "18"

# إعدادات إعادة التوجيه للـ API والتطبيق
[[redirects]]
  from = "/api/news"
  to = "/.netlify/functions/news"
  status = 200

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# إعادة توجيه SPA - يجب أن تكون الأخيرة
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# إعدادات CORS شاملة للنطاق المخصص
[[headers]]
  for = "/*"
  [headers.values]
    # CORS Headers للنطاق المخصص
    Access-Control-Allow-Origin = "https://hodifatech.com"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Access-Control-Allow-Credentials = "true"

    # Security Headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"

    # Firebase Headers للدردشة
    Access-Control-Expose-Headers = "Content-Length, Content-Range"

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "https://hodifatech.com"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Access-Control-Allow-Credentials = "true"

[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "https://hodifatech.com"
    Access-Control-Allow-Methods = "GET, POST, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin"
    Access-Control-Allow-Credentials = "true"

# إعدادات خاصة للملفات الثابتة
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"
