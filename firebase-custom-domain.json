{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "destination": "/api/index.html"}, {"source": "**", "destination": "/index.html"}], "redirects": [{"source": "/old-about", "destination": "/about", "type": 301}, {"source": "/old-projects", "destination": "/projects", "type": 301}, {"source": "/news", "destination": "/tech-news", "type": 301}, {"source": "/blog", "destination": "/tech-news", "type": 301}, {"source": "/portfolio", "destination": "/projects", "type": 301}, {"source": "/cv", "destination": "/about", "type": 301}], "headers": [{"source": "**", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://hodifatech.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept, Origin"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}]}], "cleanUrls": true, "trailingSlash": false}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}]}