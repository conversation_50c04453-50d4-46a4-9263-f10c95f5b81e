import React, { useState, useEffect, useRef } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageCircle, Users, Shield, Zap, Send, Clock, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { initializeApp } from 'firebase/app';
import { getDatabase, ref, push, onValue, off } from 'firebase/database';

// استخدام Firebase العالمي من index.html
let database: any = null;

// دالة للحصول على Firebase Database
const getFirebaseDatabase = () => {
  if (typeof window !== 'undefined') {
    // محاولة الحصول على Firebase العالمي
    if ((window as any).firebaseDatabase) {
      console.log('✅ Using global Firebase database');
      return (window as any).firebaseDatabase;
    }

    // إذا لم يكن متاح، انتظار قليلاً ثم المحاولة مرة أخرى
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 10;

      const checkFirebase = () => {
        attempts++;
        if ((window as any).firebaseDatabase) {
          console.log('✅ Firebase database found after waiting');
          resolve((window as any).firebaseDatabase);
        } else if (attempts < maxAttempts) {
          setTimeout(checkFirebase, 500);
        } else {
          console.warn('⚠️ Firebase database not found after waiting');
          // محاولة إعداد Firebase محلياً كبديل
          try {
            const firebaseConfig = {
              apiKey: "AIzaSyC6NWVYptFUzjy8mNMzyuwC4PDTrRDVmU4",
              authDomain: "myprofilewebsitechatproject.firebaseapp.com",
              databaseURL: "https://myprofilewebsitechatproject-default-rtdb.europe-west1.firebasedatabase.app",
              projectId: "myprofilewebsitechatproject",
              storageBucket: "myprofilewebsitechatproject.firebasestorage.app",
              messagingSenderId: "868130500021",
              appId: "1:868130500021:web:f9e511213975f749793dfc"
            };

            const app = initializeApp(firebaseConfig);
            const localDatabase = getDatabase(app);
            console.log('✅ Firebase initialized locally as fallback');
            resolve(localDatabase);
          } catch (error) {
            console.error('❌ Firebase local initialization failed:', error);
            resolve(null);
          }
        }
      };

      checkFirebase();
    });
  }
  return null;
};

// تعريف نوع الرسالة
interface Message {
  id: string;
  username: string;
  message: string;
  timestamp: number;
  userId: string;
}

// تعريف نوع المستخدم
interface User {
  username: string;
  userId: string;
}

const GroupChat: React.FC = () => {
  // حالات المكون
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const [tempUsername, setTempUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
  const [error, setError] = useState<string | null>(null);

  // مراجع للعناصر
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // دالة للتمرير إلى أسفل الدردشة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // تأثير للتمرير عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تأثير لتحميل المستخدم من localStorage
  useEffect(() => {
    const savedUser = localStorage.getItem('chatUser');
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('chatUser');
      }
    }
  }, []);

  // تأثير لتحميل الرسائل من Firebase
  useEffect(() => {
    if (!user) {
      setConnectionStatus('disconnected');
      return;
    }

    const initializeFirebase = async () => {
      try {
        setConnectionStatus('connecting');

        // الحصول على Firebase Database
        const firebaseDb = await getFirebaseDatabase();

        if (!firebaseDb) {
          setError('لا يمكن الاتصال بقاعدة البيانات');
          setConnectionStatus('disconnected');
          return;
        }

        database = firebaseDb;

        // استخدام Firebase العالمي أو المحلي
        let messagesRef: any;
        let unsubscribe: any;

        if ((window as any).firebaseDatabase) {
        // استخدام Firebase العالمي (compat API)
        messagesRef = (window as any).firebaseDatabase.ref('messages');
        unsubscribe = messagesRef.on('value', (snapshot: any) => {
          try {
            setConnectionStatus('connected');
            setError(null);

            const data = snapshot.val();
            if (data) {
              const messagesList: Message[] = Object.keys(data).map(key => ({
                id: key,
                ...data[key]
              }));

              // ترتيب الرسائل حسب التوقيت
              messagesList.sort((a, b) => a.timestamp - b.timestamp);
              setMessages(messagesList);
            } else {
              setMessages([]);
            }
          } catch (error) {
            console.error('Error processing messages:', error);
            setError('خطأ في معالجة الرسائل');
            setConnectionStatus('disconnected');
          }
        }, (error: any) => {
          console.error('Firebase connection error:', error);
          setError('خطأ في الاتصال بقاعدة البيانات');
          setConnectionStatus('disconnected');
        });
      } else {
        // استخدام Firebase المحلي (modern API)
        const messagesRefLocal = ref(database, 'messages');
        unsubscribe = onValue(messagesRefLocal, (snapshot) => {
          try {
            setConnectionStatus('connected');
            setError(null);

            const data = snapshot.val();
            if (data) {
              const messagesList: Message[] = Object.keys(data).map(key => ({
                id: key,
                ...data[key]
              }));

              // ترتيب الرسائل حسب التوقيت
              messagesList.sort((a, b) => a.timestamp - b.timestamp);
              setMessages(messagesList);
            } else {
              setMessages([]);
            }
          } catch (error) {
            console.error('Error processing messages:', error);
            setError('خطأ في معالجة الرسائل');
            setConnectionStatus('disconnected');
          }
        }, (error: any) => {
          console.error('Firebase connection error:', error);
          setError('خطأ في الاتصال بقاعدة البيانات');
          setConnectionStatus('disconnected');
        });
      }

      // تنظيف المستمع عند إلغاء تحميل المكون
      return () => {
        if (messagesRef && (window as any).firebaseDatabase) {
          messagesRef.off('value', unsubscribe);
        } else if (messagesRef) {
          off(messagesRef, 'value', unsubscribe);
        }
      };
    } catch (error) {
      console.error('Firebase setup error:', error);
      setError('خطأ في إعداد قاعدة البيانات');
      setConnectionStatus('disconnected');
    }
    };

    initializeFirebase();
  }, [user]);

  // دالة لحفظ اسم المستخدم
  const handleSaveUsername = () => {
    if (tempUsername.trim().length < 2) {
      alert('يجب أن يكون الاسم المستعار أكثر من حرفين');
      return;
    }

    const userId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
    const newUser: User = {
      username: tempUsername.trim(),
      userId: userId
    };

    setUser(newUser);
    localStorage.setItem('chatUser', JSON.stringify(newUser));
    setTempUsername('');
  };

  // دالة لإرسال رسالة جديدة
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || isLoading) return;

    setIsLoading(true);

    try {
      const messageData = {
        username: user.username,
        message: newMessage.trim(),
        timestamp: Date.now(),
        userId: user.userId
      };

      if ((window as any).firebaseDatabase) {
        // استخدام Firebase العالمي (compat API)
        const messagesRef = (window as any).firebaseDatabase.ref('messages');
        await messagesRef.push(messageData);
      } else {
        // استخدام Firebase المحلي (modern API)
        const messagesRef = ref(database, 'messages');
        await push(messagesRef, messageData);
      }

      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة للتعامل مع الضغط على Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!user) {
        handleSaveUsername();
      } else {
        handleSendMessage();
      }
    }
  };

  // دالة لتنسيق التوقيت
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />

      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الدردشة</span> الجماعية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              تواصل مع زوار الموقع الآخرين في دردشة جماعية مباشرة وآمنة
            </p>

            {/* ميزات الدردشة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
              <Card className="bg-gray-900/30 border-amber-500/20">
                <CardContent className="p-4 text-center">
                  <Zap className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-amber-400 mb-1">مباشرة</h3>
                  <p className="text-xs text-gray-400">رسائل فورية بدون تأخير</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-green-500/20">
                <CardContent className="p-4 text-center">
                  <Users className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-400 mb-1">جماعية</h3>
                  <p className="text-xs text-gray-400">تواصل مع عدة أشخاص</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-blue-500/20">
                <CardContent className="p-4 text-center">
                  <Shield className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-400 mb-1">آمنة</h3>
                  <p className="text-xs text-gray-400">بدون تسجيل أو بيانات شخصية</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-purple-500/20">
                <CardContent className="p-4 text-center">
                  <MessageCircle className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-400 mb-1">سهلة</h3>
                  <p className="text-xs text-gray-400">واجهة بسيطة وسهلة الاستخدام</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* مكون الدردشة */}
          <div className="mb-12">
            <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl text-amber-400 flex items-center gap-2">
                  <MessageCircle className="w-6 h-6" />
                  الدردشة المباشرة
                  <div className="flex items-center gap-2 ml-auto">
                    {connectionStatus === 'connected' && (
                      <div className="flex items-center gap-1 text-green-400 text-sm">
                        <Wifi className="w-4 h-4" />
                        متصل
                      </div>
                    )}
                    {connectionStatus === 'connecting' && (
                      <div className="flex items-center gap-1 text-yellow-400 text-sm">
                        <Wifi className="w-4 h-4 animate-pulse" />
                        جاري الاتصال...
                      </div>
                    )}
                    {connectionStatus === 'disconnected' && (
                      <div className="flex items-center gap-1 text-red-400 text-sm">
                        <WifiOff className="w-4 h-4" />
                        غير متصل
                      </div>
                    )}
                    <span className="text-sm text-gray-400">
                      {messages.length} رسالة
                    </span>
                  </div>
                </CardTitle>
                {error && (
                  <div className="mt-2 p-2 bg-red-900/50 border border-red-700 rounded-lg flex items-center gap-2 text-red-300">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {!database ? (
                  // رسالة خطأ Firebase
                  <div className="text-center py-8">
                    <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-400" />
                    <h3 className="text-xl mb-4 text-red-400">خطأ في الاتصال بقاعدة البيانات</h3>
                    <p className="text-gray-400 mb-4">
                      عذراً، لا يمكن الاتصال بخدمة الدردشة حالياً. يرجى المحاولة لاحقاً.
                    </p>
                    <Button
                      onClick={() => window.location.reload()}
                      className="bg-amber-500 hover:bg-amber-600 text-black"
                    >
                      إعادة المحاولة
                    </Button>
                  </div>
                ) : !user ? (
                  // نموذج إدخال اسم المستخدم
                  <div className="text-center py-8">
                    <h3 className="text-xl mb-4">أدخل اسمك المستعار للانضمام للدردشة</h3>
                    <div className="max-w-md mx-auto flex gap-2">
                      <Input
                        type="text"
                        placeholder="اسمك المستعار..."
                        value={tempUsername}
                        onChange={(e) => setTempUsername(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="bg-gray-700 border-gray-600 text-white"
                        maxLength={20}
                      />
                      <Button
                        onClick={handleSaveUsername}
                        className="bg-amber-500 hover:bg-amber-600 text-black"
                        disabled={tempUsername.trim().length < 2}
                      >
                        انضمام
                      </Button>
                    </div>
                  </div>
                ) : (
                  // واجهة الدردشة
                  <div className="space-y-4">
                    {/* منطقة الرسائل */}
                    <div className="h-96 overflow-y-auto bg-gray-900/50 rounded-lg p-4 space-y-3">
                      {messages.length === 0 ? (
                        <div className="text-center text-gray-400 py-8">
                          <MessageCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                          <p>لا توجد رسائل بعد. كن أول من يبدأ المحادثة!</p>
                        </div>
                      ) : (
                        messages.map((msg) => (
                          <div
                            key={msg.id}
                            className={`flex ${msg.userId === user.userId ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                msg.userId === user.userId
                                  ? 'bg-amber-500 text-black'
                                  : 'bg-gray-700 text-white'
                              }`}
                            >
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-semibold text-sm">
                                  {msg.username}
                                </span>
                                <span className="text-xs opacity-70 flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatTime(msg.timestamp)}
                                </span>
                              </div>
                              <p className="text-sm break-words">{msg.message}</p>
                            </div>
                          </div>
                        ))
                      )}
                      <div ref={messagesEndRef} />
                    </div>

                    {/* نموذج إرسال الرسائل */}
                    <div className="flex gap-2">
                      <Input
                        type="text"
                        placeholder="اكتب رسالتك..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="bg-gray-700 border-gray-600 text-white flex-1"
                        maxLength={500}
                        disabled={isLoading}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isLoading}
                        className="bg-amber-500 hover:bg-amber-600 text-black"
                      >
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* معلومات المستخدم */}
                    <div className="text-center text-sm text-gray-400">
                      مرحباً <span className="text-amber-400">{user.username}</span>
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => {
                          setUser(null);
                          localStorage.removeItem('chatUser');
                        }}
                        className="text-gray-400 hover:text-white ml-2"
                      >
                        تغيير الاسم
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* معلومات إضافية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-gray-900/30 border-amber-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-amber-400 mb-4 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  كيفية الاستخدام
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span>
                    <p>أدخل اسمك المستعار (لا يتطلب تسجيل)</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span>
                    <p>انضم إلى الدردشة وشاهد الرسائل السابقة</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span>
                    <p>اكتب رسالتك واضغط Enter أو زر الإرسال</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</span>
                    <p>تفاعل مع الزوار الآخرين في الوقت الفعلي</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/30 border-green-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-green-400 mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  الخصوصية والأمان
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نطلب أي معلومات شخصية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الاسم المستعار يُحفظ محلياً فقط</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الرسائل مشفرة ومحمية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>يمكنك تغيير اسمك في أي وقت</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نتتبع أو نحفظ بياناتك الشخصية</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* ملاحظة تقنية */}
          <div className="mt-12">
            <Card className="bg-blue-900/20 border-blue-500/30">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-bold text-blue-400 mb-2">⚡ تقنية متقدمة</h3>
                <p className="text-gray-300 text-sm">
                  تم بناء هذه الدردشة باستخدام <span className="text-amber-400 font-semibold">React</span> و
                  <span className="text-amber-400 font-semibold"> TypeScript</span> و
                  <span className="text-amber-400 font-semibold"> Firebase Realtime Database</span>
                  لضمان أداء سريع وموثوق ومجاني 100%
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default GroupChat;
