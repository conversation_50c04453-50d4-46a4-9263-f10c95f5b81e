// Netlify Function لجلب الأخبار التقنية
// استخدام fetch المدمج في Node.js 18+

exports.handler = async (event, context) => {
  // إعداد CORS headers للنطاق المخصص
  const allowedOrigins = [
    'https://hodifatech.com',
    'https://www.hodifatech.com',
    'https://hodifa-tech-profile-main.netlify.app',
    'https://myprofilewebsitechatproject.web.app',
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:8080'
  ];

  const origin = event.headers.origin || event.headers.Origin;
  const allowOrigin = allowedOrigins.includes(origin) ? origin : 'https://hodifatech.com';

  const headers = {
    'Access-Control-Allow-Origin': allowOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // التعامل مع preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // السماح فقط بـ GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    console.log('🔄 Fetching tech news from multiple sources...');

    // استخدام NewsAPI.org مع API key من environment variables
    const API_KEY = process.env.NEWS_API_KEY || '1660ff496c4247c3a7d49457501feb73';

    // مصادر الأخبار المختلفة - التركيز على الأخبار العربية
    const newsSources = [
      {
        name: "NewsAPI.org - Arabic Tech Primary",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "تقنية OR تكنولوجيا OR ذكاء اصطناعي",
          language: "ar",
          sortBy: "publishedAt",
          pageSize: "10",
          apiKey: API_KEY
        }
      },
      {
        name: "NewsAPI.org - Arabic Tech Secondary",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "برمجة OR تطوير OR حاسوب OR إنترنت OR تطبيقات",
          language: "ar",
          sortBy: "publishedAt",
          pageSize: "6",
          apiKey: API_KEY
        }
      },
      {
        name: "NewsAPI.org - English Tech Fallback",
        url: "https://newsapi.org/v2/everything",
        params: {
          q: "technology OR programming OR AI OR software",
          language: "en",
          sortBy: "publishedAt",
          pageSize: "4",
          apiKey: API_KEY
        }
      }
    ];

    let allArticles = [];
    let successfulSources = [];

    // جرب كل مصدر
    for (const source of newsSources) {
      try {
        console.log(`🔄 Trying ${source.name}...`);

        const queryParams = new URLSearchParams(source.params).toString();
        const apiUrl = `${source.url}?${queryParams}`;

        const response = await fetch(apiUrl);

        if (response.ok) {
          const data = await response.json();

          if (data.articles && data.articles.length > 0) {
            const processedArticles = data.articles
              .filter(article =>
                article.title &&
                article.description &&
                article.url &&
                !article.title.toLowerCase().includes('[removed]') &&
                !article.description.toLowerCase().includes('[removed]')
              )
              .map((article, index) => ({
                article_id: `${source.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_${index}`,
                title: article.title,
                description: article.description,
                content: article.content,
                link: article.url,
                image_url: article.urlToImage,
                source_id: article.source?.name || source.name,
                category: ["technology"],
                pubDate: article.publishedAt,
                language: source.params.language
              }));

            if (processedArticles.length > 0) {
              allArticles = [...allArticles, ...processedArticles];
              successfulSources.push(source.name);
              console.log(`✅ ${source.name} returned ${processedArticles.length} articles`);
            }
          }
        } else {
          console.warn(`❌ ${source.name} API call failed with status: ${response.status}`);
        }
      } catch (sourceError) {
        console.warn(`❌ ${source.name} failed:`, sourceError);
        continue;
      }
    }

    // ترتيب المقالات حسب التاريخ وأخذ أحدث 12 مقال
    const filteredArticles = allArticles
      .sort((a, b) => new Date(b.pubDate).getTime() - new Date(a.pubDate).getTime())
      .slice(0, 12);

    // إذا فشلت جميع المصادر، استخدم أخبار ثابتة محدثة
    if (filteredArticles.length === 0) {
      console.log('📰 Using fallback static news');
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(getFallbackNews())
      };
    }

    console.log(`✅ Successfully fetched ${filteredArticles.length} articles from ${successfulSources.join(', ')}`);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        source: successfulSources.join(', '),
        articles: filteredArticles,
        timestamp: new Date().toISOString(),
        count: filteredArticles.length
      })
    };

  } catch (error) {
    console.error('Error fetching news:', error);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(getFallbackNews())
    };
  }
};

function getFallbackNews() {
  const fallbackNews = [
    {
      article_id: "static_ar_1",
      title: "الذكاء الاصطناعي يغير مستقبل التقنية في العالم العربي",
      description: "تشهد المنطقة العربية نمواً متسارعاً في تطبيقات الذكاء الاصطناعي، مع استثمارات ضخمة في التقنيات الحديثة والابتكار التكنولوجي.",
      link: "https://www.ai-arabia.com",
      image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
      source_id: "تقنية عربية",
      category: ["ذكاء اصطناعي", "تقنية"],
      pubDate: "2025-01-15T10:30:00Z",
      language: "ar"
    },
    {
      article_id: "static_ar_2",
      title: "تطوير تطبيقات الهواتف الذكية باستخدام React Native في المنطقة العربية",
      description: "يشهد سوق تطوير التطبيقات في العالم العربي نمواً كبيراً، مع تزايد الطلب على تطبيقات الهواتف الذكية المطورة بتقنيات حديثة.",
      link: "https://www.react-native-arabia.com",
      image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
      source_id: "تطوير عربي",
      category: ["تطوير تطبيقات", "برمجة"],
      pubDate: "2025-01-14T09:15:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_1",
      title: "React 19 Brings Revolutionary Features for Modern UI Development",
      description: "The new React version introduces enhanced Server Components, a new Compiler, and advanced features that make it easier for developers to build high-performance web applications.",
      link: "https://react.dev",
      image_url: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
      source_id: "React Blog",
      category: ["web development", "React"],
      pubDate: "2025-01-14T14:20:00Z",
      language: "en"
    },
    {
      article_id: "static_ar_3",
      title: "تقنيات البلوك تشين والعملات الرقمية في الشرق الأوسط",
      description: "تشهد منطقة الشرق الأوسط اهتماماً متزايداً بتقنيات البلوك تشين والعملات الرقمية، مع إطلاق مشاريع رائدة في هذا المجال.",
      link: "https://www.blockchain-arabia.com",
      image_url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=500&h=300&fit=crop",
      source_id: "بلوك تشين عربي",
      category: ["بلوك تشين", "تقنية مالية"],
      pubDate: "2025-01-13T09:15:00Z",
      language: "ar"
    },
    {
      article_id: "static_ar_4",
      title: "الأمن السيبراني في عصر الذكاء الاصطناعي والحوسبة الكمية",
      description: "مع ظهور تقنيات الذكاء الاصطناعي والحوسبة الكمية، تواجه الشركات تحديات أمنية جديدة تتطلب استراتيجيات حماية متطورة ومبتكرة.",
      link: "https://www.cybersecurity-arabia.com",
      image_url: "https://images.unsplash.com/photo-1563986768609-322da13575f3?w=500&h=300&fit=crop",
      source_id: "أمن المعلومات العربي",
      category: ["أمن المعلومات", "ذكاء اصطناعي"],
      pubDate: "2025-01-12T09:15:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_2",
      title: "Flutter 3.27 Brings Revolutionary Improvements for Cross-Platform Development",
      description: "The new Flutter version offers enhanced performance, advanced development tools, and better support for Material Design 3 and desktop applications.",
      link: "https://flutter.dev",
      image_url: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
      source_id: "Flutter Team",
      category: ["app development", "Flutter"],
      pubDate: "2025-01-12T16:45:00Z",
      language: "en"
    },
    {
      article_id: "static_ar_3",
      title: "ثورة قواعد البيانات: Vector Databases والذكاء الاصطناعي",
      description: "قواعد البيانات الشعاعية تصبح العمود الفقري لتطبيقات الذكاء الاصطناعي الحديثة، مع دعم متقدم للبحث الدلالي والتعلم الآلي.",
      link: "https://www.pinecone.io",
      image_url: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop",
      source_id: "AI Database Weekly",
      category: ["قواعد البيانات", "ذكاء اصطناعي"],
      pubDate: "2025-01-11T11:30:00Z",
      language: "ar"
    },
    {
      article_id: "static_en_3",
      title: "WebAssembly 2025: The Future of High-Performance Computing in Browsers",
      description: "WebAssembly evolves to support more languages and features, with new capabilities for parallel computing and AI in browsers.",
      link: "https://webassembly.org",
      image_url: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
      source_id: "WebAssembly Foundation",
      category: ["web development", "WebAssembly"],
      pubDate: "2025-01-10T13:20:00Z",
      language: "en"
    }
  ];

  return {
    success: true,
    source: "Static Fallback",
    articles: fallbackNews,
    timestamp: new Date().toISOString(),
    count: fallbackNews.length
  };
}
